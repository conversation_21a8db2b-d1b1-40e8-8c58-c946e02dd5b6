import { createClient } from "@/utils/supabase/client";

interface Variable {
  data: string;
  name: string;
  links: any[];
}

interface VariableContainer {
  vars: Variable[];
}

interface Template {
  id: string;
  name: string;
}

interface Dataset {
  id: string;
  name: string;
}

const supabase = createClient();

const mergeVariables = (baseVars: VariableContainer, overrideVars: VariableContainer): VariableContainer => {
  if (!overrideVars?.vars) return baseVars;
  if (!baseVars?.vars) return overrideVars;

  const mergedVarsMap = new Map(
    baseVars.vars.map(v => [v.name, v])
  );

  overrideVars.vars.forEach(overrideVar => {
    mergedVarsMap.set(overrideVar.name, overrideVar);
  });

  return {
    vars: Array.from(mergedVarsMap.values())
  };
};

export async function processData(selectedTemplates: Template[], selectedDatasets: Dataset[]) {
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) { // Add check for null user
      console.error('Error fetching user or user is null:', userError);
      return; // Exit if no user
    }

    const userId = user.id; // Now safe to access user.id
    const tasks = [];

    const { data: job, error: jobError } = await supabase
      .from('jobs')
      .insert({
        user_id: userId,
        name: `Job ${new Date().toISOString()}`,
        description: `Job created for ${selectedTemplates.length} templates and ${selectedDatasets.length} datasets`,
        status: 'queued'
      })
      .select()
      .single();

    if (jobError) {
      console.error('Error creating job:', jobError);
      return;
    }

    for (const template of selectedTemplates) {
      const { data: templateData, error: templateError } = await supabase
        .from('global_job_templates')
        .select('template_data, vars')
        .eq('id', template.id)
        .single();

      if (templateError) {
        console.error('Error fetching template data:', templateError);
        continue;
      }

      for (const dataset of selectedDatasets) {
        // Fetch dataset with variable overrides
        const { data: datasetData, error: datasetDetailError } = await supabase
          .from('datasets')
          .select('variable_overrides')
          .eq('id', dataset.id)
          .single();

        if (datasetDetailError) {
          console.error('Error fetching dataset details:', datasetDetailError);
          continue;
        }

        const { data: datasetFiles, error: datasetError } = await supabase
          .from('dataset_files')
          .select('file_id, file_type')
          .eq('dataset_id', dataset.id);

        if (datasetError) {
          console.error('Error fetching dataset files:', datasetError);
          continue;
        }

        // Merge variables with priority: template < category (future) < dataset
        let finalVars = templateData.vars as VariableContainer;
        
        // Here you can add category vars merging when implemented
        // finalVars = mergeVariables(finalVars, categoryVars);
        
        // Merge dataset overrides
        finalVars = mergeVariables(finalVars, datasetData.variable_overrides);

        // Ensure finalVars and finalVars.vars exist
        if (!finalVars) {
          finalVars = { vars: [] };
        }
        if (!finalVars.vars) {
          finalVars.vars = [];
        }

        // Add or update DSNAME variable using dataset.name
        const dsnameVarIndex = finalVars.vars.findIndex(v => v.name === 'DSNAME');
        if (dsnameVarIndex > -1) {
          // Update existing DSNAME
          finalVars.vars[dsnameVarIndex].data = dataset.name;
        } else {
          // Add new DSNAME
          finalVars.vars.push({ name: 'DSNAME', data: dataset.name, links: [] });
        }

        // Define the type for the accumulator explicitly
        const fileTypeMap = datasetFiles.reduce((acc: Record<string, string[]>, file) => {
          const fileType = file.file_type as string; // Assert file_type as string if needed, or handle null/undefined
          const fileId = file.file_id as string;   // Assert file_id as string if needed

          if (!acc[fileType]) {
            acc[fileType] = [];
          }
          acc[fileType].push(fileId);
          return acc;
        }, {} as Record<string, string[]>); // Initialize with the correct type

        const workervars = Object.entries(fileTypeMap).map(([fileType, fileIds]) => ({
          name: fileType,
          file_ids: fileIds
        }));

        tasks.push({
          user_id: userId,
          name: `${template.name} - ${dataset.name}`,
          description: `Task created from template ${template.name} for dataset ${dataset.name}`,
          global_job_template_id: template.id,
          dataset_id: dataset.id,
          job_json: templateData.template_data,
          vars: finalVars,
          workervars: workervars,
          job_id: job.id,
          status: 'queued'
        });
      }
    }

    const { data, error } = await supabase.from('tasks').insert(tasks);
    if (error) {
      console.error('Error creating tasks:', error);
      await supabase.from('jobs').delete().eq('id', job.id);
      return;
    }

    return { tasks: tasks, job: job };
}
