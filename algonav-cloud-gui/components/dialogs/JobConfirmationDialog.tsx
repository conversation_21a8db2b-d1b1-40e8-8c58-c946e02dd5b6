import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  List,
  ListItem,
  ListItemText,
  Collapse,
  Typography,
  TextField,
  Box
} from '@mui/material';
import { ExpandLess, ExpandMore } from '@mui/icons-material';

interface JobConfirmationDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: (jobName: string) => void;
  template: any;
  datasets: any[];
}

export const JobConfirmationDialog = ({
  open,
  onClose,
  onConfirm,
  template,
  datasets
}: JobConfirmationDialogProps) => {
  const [expandedDatasets, setExpandedDatasets] = React.useState(false);
  const [jobName, setJobName] = React.useState('');

  // Reset job name when dialog opens/closes
  React.useEffect(() => {
    if (!open) {
      setJobName('');
    }
  }, [open]);

  const handleConfirm = () => {
    onConfirm(jobName.trim());
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>Confirm Job Creation</DialogTitle>
      <DialogContent>
        <Box sx={{ mb: 3 }}>
          <TextField
            fullWidth
            label="Job Name (optional)"
            placeholder="Enter a custom job name..."
            value={jobName}
            onChange={(e) => setJobName(e.target.value)}
            size="small"
            helperText="If empty, a default name will be generated"
            sx={{ mb: 2 }}
            autoFocus
          />
        </Box>

        <Typography variant="subtitle1" gutterBottom>
          Template: {template?.name}
        </Typography>

        <ListItem button onClick={() => setExpandedDatasets(!expandedDatasets)}>
          <ListItemText primary={`Selected Datasets (${datasets.length})`} />
          {expandedDatasets ? <ExpandLess /> : <ExpandMore />}
        </ListItem>
        
        <Collapse in={expandedDatasets}>
          <List>
            {datasets.map((dataset) => (
              <ListItem key={dataset.id}>
                <ListItemText primary={dataset.name} />
              </ListItem>
            ))}
          </List>
        </Collapse>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="primary">
          Cancel
        </Button>
        <Button onClick={handleConfirm} variant="contained" color="primary">
          Create Job
        </Button>
      </DialogActions>
    </Dialog>
  );
};
