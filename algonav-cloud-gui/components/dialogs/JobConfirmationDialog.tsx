import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  List,
  ListItem,
  ListItemText,
  Collapse,
  Typography
} from '@mui/material';
import { ExpandLess, ExpandMore } from '@mui/icons-material';

interface JobConfirmationDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  template: any;
  datasets: any[];
  jobName?: string;
}

export const JobConfirmationDialog = ({
  open,
  onClose,
  onConfirm,
  template,
  datasets,
  jobName
}: JobConfirmationDialogProps) => {
  const [expandedDatasets, setExpandedDatasets] = React.useState(false);

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>Confirm Job Creation</DialogTitle>
      <DialogContent>
        <Typography variant="subtitle1" gutterBottom>
          Job Name: {jobName?.trim() || 'Auto-generated'}
        </Typography>

        <Typography variant="subtitle1" gutterBottom>
          Template: {template?.name}
        </Typography>

        <ListItem button onClick={() => setExpandedDatasets(!expandedDatasets)}>
          <ListItemText primary={`Selected Datasets (${datasets.length})`} />
          {expandedDatasets ? <ExpandLess /> : <ExpandMore />}
        </ListItem>
        
        <Collapse in={expandedDatasets}>
          <List>
            {datasets.map((dataset) => (
              <ListItem key={dataset.id}>
                <ListItemText primary={dataset.name} />
              </ListItem>
            ))}
          </List>
        </Collapse>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="primary">
          Cancel
        </Button>
        <Button onClick={onConfirm} variant="contained" color="primary">
          Create Job
        </Button>
      </DialogActions>
    </Dialog>
  );
};
